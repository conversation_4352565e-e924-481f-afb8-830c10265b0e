const express = require('express');
const router = express.Router();
const lgdGeminiService = require('../services/lgdGemini');
const supabase = require('../supabaseClient');

// GET all LGD evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('lgd_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching LGD evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch LGD evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /lgd-evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch LGD evaluations' });
  }
});

// POST run new LGD evaluation
router.post('/run', async (req, res) => {
  try {
    const { input } = req.body;

    if (!input) {
      return res.status(400).json({ error: 'Input is required' });
    }

    // Validate LGD input structure
    if (!input.transcript || !input.competencies) {
      return res.status(400).json({
        error: 'Transcript and competencies are required'
      });
    }

    // Get prompt ID from input, default to 3 if not provided
    const promptId = input.promptId || 3;
    const formattingPromptId = 4; // Formatting prompt is always ID 4

    // Get LGD prompts from Supabase
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .in('id', [promptId, formattingPromptId]);

    if (promptsError || !prompts || prompts.length < 2) {
      console.error('Error fetching LGD prompts:', promptsError);
      return res.status(400).json({ error: 'Required LGD prompts not found in Supabase' });
    }

    const prompt1 = prompts.find(p => p.id === promptId); // LGD Analysis prompt
    const prompt2 = prompts.find(p => p.id === formattingPromptId); // LGD Formatting prompt

    if (!prompt1 || !prompt2) {
      return res.status(400).json({ error: 'Required LGD prompts not found in Supabase' });
    }

    // Run the LGD prompt chain
    const result = await lgdGeminiService.runLGDPromptChain(input, prompt1.content, prompt2.content);

    // Format input for display
    const formattedInput = `Transcript: ${input.transcript.substring(0, 200)}...\nCompetencies: ${input.competencies.substring(0, 200)}...`;

    // Insert LGD evaluation result into Supabase
    const evaluationToInsert = {
      input,
      "formattedInput": formattedInput,
      output: result.finalOutput,
      "prompt1Version": prompt1.version,
      "prompt2Version": prompt2.version,
      "prompt1Content": prompt1.content,
      "prompt2Content": prompt2.content,
      timestamp: new Date().toISOString(),
      details: result
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('lgd_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting LGD evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save LGD evaluation' });
    }

    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /lgd-evaluations/run:', error);
    res.status(500).json({ error: 'Failed to run LGD evaluation' });
  }
});

module.exports = router;
