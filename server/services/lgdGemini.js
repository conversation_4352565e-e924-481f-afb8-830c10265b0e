class LGDGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(prompt) {
    try {
      await this.initPromise; // Ensure client is initialized
      const response = await this.client.models.generateContent({
        model: 'gemini-2.5-flash-preview-05-20',
        contents: prompt,
        config: {
          temperature: 0.2,
          responseMimeType: "application/json"
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response from <PERSON>');
    }
  }

  async runLGDPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized
      
      const { transcript, competencies } = input;

      // Step 1: Run LGD analysis prompt with transcript and competencies
      const fullAnalysisPrompt = analysisPrompt
        .replace('{{lgd_competencies}}', competencies)
        + '\n\n# LGD Transcript\n' + transcript;

      console.log('Running LGD analysis prompt...');
      const analysisOutput = await this.generateResponse(fullAnalysisPrompt);

      // Step 2: Run formatting prompt with analysis output
      const fullFormattingPrompt = formattingPrompt + '\n\n# Analysis to Format:\n' + analysisOutput;

      console.log('Running LGD formatting prompt...');
      const finalOutput = await this.generateResponse(fullFormattingPrompt);

      return {
        step1: {
          prompt: fullAnalysisPrompt,
          output: analysisOutput
        },
        step2: {
          prompt: fullFormattingPrompt,
          output: finalOutput
        },
        finalOutput
      };
    } catch (error) {
      console.error('Error in LGD prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new LGDGeminiService();
