import React, { useState } from 'react';
import PromptModal from './PromptModal';
import { promptsApi } from '../services/api';

const LGDResultsTable = ({ evaluations }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [modalState, setModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const handlePromptVersionClick = async (promptId, version, promptName, evaluation = null) => {
    setModalState({
      isOpen: true,
      promptData: null,
      promptName,
      loading: true
    });

    try {
      // First try to use stored content from evaluation if available
      if (evaluation) {
        const storedContent = promptId === 3 ? evaluation.prompt1Content : evaluation.prompt2Content;
        if (storedContent) {
          setModalState(prev => ({
            ...prev,
            promptData: {
              content: storedContent,
              version: version,
              updatedAt: evaluation.timestamp
            },
            loading: false
          }));
          return;
        }
      }

      // Fallback to API call for historical versions
      const response = await promptsApi.getVersion(promptId, version);
      setModalState(prev => ({
        ...prev,
        promptData: response.data,
        loading: false
      }));
    } catch (error) {
      console.error('Error fetching prompt version:', error);
      setModalState(prev => ({
        ...prev,
        loading: false
      }));
    }
  };

  const formatAnalysisOutput = (output) => {
    if (typeof output === 'string') {
      try {
        const parsed = JSON.parse(output);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return output;
      }
    }
    return JSON.stringify(output, null, 2);
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '40px',
        color: '#6c757d',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}>
        <h3>No LGD Analysis Results</h3>
        <p>Run an LGD analysis to see results here.</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ marginBottom: '20px', color: '#495057' }}>
        📊 LGD Analysis Results ({evaluations.length})
      </h2>
      
      <div style={{
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        overflow: 'hidden',
        backgroundColor: 'white'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead style={{ backgroundColor: '#f8f9fa' }}>
            <tr>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Prompt Versions
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Input Summary
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div>
                      P3: <button
                        onClick={() => handlePromptVersionClick(3, evaluation.prompt1Version, 'LGD Analysis Prompt', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt1Version}
                      </button>
                    </div>
                    <div>
                      P4: <button
                        onClick={() => handlePromptVersionClick(4, evaluation.prompt2Version, 'LGD Formatting Prompt', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt2Version}
                      </button>
                    </div>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div style={{ fontSize: '14px', color: '#495057' }}>
                      <strong>Transcript:</strong> {evaluation.formattedInput?.substring(0, 100)}...
                    </div>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: expandedRow === evaluation.id ? '#007bff' : 'white',
                        color: expandedRow === evaluation.id ? 'white' : '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Hide' : 'Show'} Details
                    </button>
                  </td>
                </tr>
                
                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="4" style={{ padding: '20px', backgroundColor: '#f8f9fa' }}>
                      <div style={{ display: 'grid', gap: '20px' }}>
                        <div>
                          <h4 style={{ marginBottom: '10px', color: '#495057' }}>📝 Input Data</h4>
                          <pre style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '4px',
                            border: '1px solid #dee2e6',
                            fontSize: '12px',
                            overflow: 'auto',
                            maxHeight: '200px'
                          }}>
                            {evaluation.formattedInput || 'No formatted input available'}
                          </pre>
                        </div>
                        
                        <div>
                          <h4 style={{ marginBottom: '10px', color: '#495057' }}>🎯 Analysis Output</h4>
                          <pre style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '4px',
                            border: '1px solid #dee2e6',
                            fontSize: '12px',
                            overflow: 'auto',
                            maxHeight: '400px'
                          }}>
                            {formatAnalysisOutput(evaluation.output)}
                          </pre>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      <PromptModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        promptData={modalState.promptData}
        promptName={modalState.promptName}
        loading={modalState.loading}
      />
    </div>
  );
};

export default LGDResultsTable;
