import axios from 'axios';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const promptsApi = {
  getAll: () => api.get('/prompts'),
  getById: (id) => api.get(`/prompts/${id}`),
  getVersion: (id, version) => api.get(`/prompts/${id}/versions/${version}`),
  update: (id, content) => api.put(`/prompts/${id}`, { content }),
};

export const evaluationsApi = {
  getAll: () => api.get('/evaluations'),
  run: (input) => api.post('/evaluations/run', { input }),
};

export const lgdEvaluationsApi = {
  getAll: () => api.get('/lgd-evaluations'),
  run: (input) => api.post('/lgd-evaluations/run', { input }),
};

export default api;
